{"version": 3, "sources": ["src/pages/subscription/components/UnifiedSubscriptionContent.tsx", "src/pages/subscription/index.tsx"], "sourcesContent": ["/**\n * 统一订阅管理内容组件\n * 整合订阅详情和套餐选择功能\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card,\n  Descriptions, \n  Button, \n  Space, \n  Typography, \n  Tag, \n  Progress,\n  message,\n  Modal,\n  Table,\n  Alert,\n  Empty,\n  Row,\n  Col,\n  List,\n  InputNumber,\n  Divider\n} from 'antd';\nimport {\n  CrownOutlined,\n  UpOutlined,\n  ReloadOutlined,\n  StopOutlined,\n  HistoryOutlined,\n  CheckOutlined,\n  StarOutlined,\n  ShoppingCartOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { SubscriptionService } from '@/services';\nimport type { \n  SubscriptionResponse, \n  SubscriptionPlanResponse, \n  CreateSubscriptionRequest \n} from '@/types/api';\nimport { SubscriptionStatus } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\ninterface UnifiedSubscriptionContentProps {\n  currentSubscription: SubscriptionResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst UnifiedSubscriptionContent: React.FC<UnifiedSubscriptionContentProps> = ({\n  currentSubscription,\n  loading,\n  onRefresh\n}) => {\n  // 订阅详情相关状态\n  const [subscriptionHistory, setSubscriptionHistory] = useState<SubscriptionResponse[]>([]);\n  const [usageInfo, setUsageInfo] = useState<any>(null);\n  const [historyModalVisible, setHistoryModalVisible] = useState(false);\n\n  // 套餐选择相关状态\n  const [plans, setPlans] = useState<SubscriptionPlanResponse[]>([]);\n  const [plansLoading, setPlansLoading] = useState(true);\n  const [subscribing, setSubscribing] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlanResponse | null>(null);\n  const [subscribeModalVisible, setSubscribeModalVisible] = useState(false);\n  const [duration, setDuration] = useState(1);\n\n  useEffect(() => {\n    fetchPlans();\n    if (currentSubscription) {\n      fetchSubscriptionHistory();\n      fetchUsageInfo();\n    }\n  }, [currentSubscription]);\n\n  // 获取套餐列表\n  const fetchPlans = async () => {\n    try {\n      setPlansLoading(true);\n      const plansData = await SubscriptionService.getActivePlans();\n      setPlans(plansData);\n    } catch (error) {\n      console.error('获取套餐列表失败:', error);\n      message.error('获取套餐列表失败');\n    } finally {\n      setPlansLoading(false);\n    }\n  };\n\n  // 获取订阅历史\n  const fetchSubscriptionHistory = async () => {\n    try {\n      const history = await SubscriptionService.getSubscriptionHistory();\n      setSubscriptionHistory(history);\n    } catch (error) {\n      console.error('获取订阅历史失败:', error);\n    }\n  };\n\n  // 获取使用情况\n  const fetchUsageInfo = async () => {\n    try {\n      const usage = await SubscriptionService.getUsageInfo();\n      setUsageInfo(usage);\n    } catch (error) {\n      console.error('获取使用情况失败:', error);\n    }\n  };\n\n  // 处理订阅\n  const handleSubscribe = async () => {\n    if (!selectedPlan) return;\n\n    try {\n      setSubscribing(true);\n      const request: CreateSubscriptionRequest = {\n        planId: selectedPlan.id,\n        duration: duration\n      };\n      \n      await SubscriptionService.createSubscription(request);\n      message.success('订阅成功！');\n      setSubscribeModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('订阅失败:', error);\n      message.error('订阅失败，请稍后重试');\n    } finally {\n      setSubscribing(false);\n    }\n  };\n\n  // 取消订阅\n  const handleCancelSubscription = async () => {\n    if (!currentSubscription) return;\n\n    Modal.confirm({\n      title: '确认取消订阅',\n      content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',\n      okText: '确认取消',\n      cancelText: '保留订阅',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          await SubscriptionService.cancelSubscription(currentSubscription.id);\n          message.success('订阅已取消');\n          onRefresh();\n        } catch (error) {\n          console.error('取消订阅失败:', error);\n          message.error('取消订阅失败');\n        }\n      }\n    });\n  };\n\n  // 获取状态标签\n  const getStatusTag = (status: SubscriptionStatus) => {\n    const statusConfig = {\n      [SubscriptionStatus.ACTIVE]: { color: 'green', text: '有效' },\n      [SubscriptionStatus.EXPIRED]: { color: 'red', text: '已过期' },\n      [SubscriptionStatus.CANCELED]: { color: 'default', text: '已取消' },\n      [SubscriptionStatus.PENDING]: { color: 'orange', text: '待激活' }\n    };\n    \n    const config = statusConfig[status] || { color: 'default', text: '未知' };\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  // 获取套餐推荐标签\n  const getPlanRecommendation = (plan: SubscriptionPlanResponse) => {\n    if (plan.name === '标准版') {\n      return <Tag color=\"orange\" icon={<StarOutlined />}>推荐</Tag>;\n    }\n    if (plan.name === '企业版') {\n      return <Tag color=\"gold\" icon={<CrownOutlined />}>热门</Tag>;\n    }\n    return null;\n  };\n\n  // 套餐特性列表\n  const getPlanFeatures = (plan: SubscriptionPlanResponse) => {\n    const features = [\n      `可创建 ${plan.maxSize === 999999 ? '无限' : plan.maxSize} 个团队`,\n      '团队成员无限制',\n      '数据安全保障',\n      '7x24小时技术支持'\n    ];\n\n    if (plan.name !== '免费版') {\n      features.push('优先客服支持');\n    }\n    if (plan.name === '企业版') {\n      features.push('定制化服务');\n      features.push('专属客户经理');\n    }\n\n    return features;\n  };\n\n  // 历史记录表格列定义\n  const historyColumns: ColumnsType<SubscriptionResponse> = [\n    {\n      title: '套餐名称',\n      dataIndex: 'planName',\n      key: 'planName',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: SubscriptionStatus) => getStatusTag(status),\n    },\n    {\n      title: '开始时间',\n      dataIndex: 'startDate',\n      key: 'startDate',\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '结束时间',\n      dataIndex: 'endDate',\n      key: 'endDate',\n      render: (date: string) => date ? new Date(date).toLocaleDateString() : '永久',\n    },\n    {\n      title: '价格',\n      dataIndex: 'price',\n      key: 'price',\n      render: (price: number) => `¥${price.toFixed(2)}`,\n    }\n  ];\n\n  return (\n    <div>\n      {/* 当前订阅状态 */}\n      <Card \n        title={\n          <Space>\n            <CrownOutlined />\n            当前订阅状态\n          </Space>\n        }\n        extra={\n          <Button \n            icon={<ReloadOutlined />} \n            onClick={onRefresh}\n            loading={loading}\n          >\n            刷新\n          </Button>\n        }\n        style={{ marginBottom: 24 }}\n      >\n        {currentSubscription ? (\n          <div>\n            <Descriptions column={2} bordered>\n              <Descriptions.Item label=\"套餐名称\">\n                <Space>\n                  {currentSubscription.planName}\n                  {getStatusTag(currentSubscription.status)}\n                </Space>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"团队限制\">\n                {currentSubscription.maxSize === 999999 ? '无限制' : `${currentSubscription.maxSize} 个`}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"开始时间\">\n                {new Date(currentSubscription.startDate).toLocaleDateString()}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"结束时间\">\n                {currentSubscription.endDate \n                  ? new Date(currentSubscription.endDate).toLocaleDateString() \n                  : '永久有效'\n                }\n              </Descriptions.Item>\n              <Descriptions.Item label=\"月费\">\n                ¥{currentSubscription.price.toFixed(2)}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"剩余天数\">\n                {usageInfo?.remainingDays !== undefined \n                  ? `${usageInfo.remainingDays} 天` \n                  : '计算中...'\n                }\n              </Descriptions.Item>\n            </Descriptions>\n\n            {usageInfo && (\n              <div style={{ marginTop: 16 }}>\n                <Text strong>团队使用情况：</Text>\n                <Progress\n                  percent={usageInfo.usagePercentage}\n                  format={() => `${usageInfo.currentUsage}/${usageInfo.maxUsage === 999999 ? '∞' : usageInfo.maxUsage}`}\n                  style={{ marginTop: 8 }}\n                />\n              </div>\n            )}\n\n            <div style={{ marginTop: 16 }}>\n              <Space>\n                <Button \n                  type=\"primary\" \n                  icon={<UpOutlined />}\n                  onClick={() => setSubscribeModalVisible(true)}\n                >\n                  升级套餐\n                </Button>\n                <Button \n                  icon={<HistoryOutlined />}\n                  onClick={() => setHistoryModalVisible(true)}\n                >\n                  查看历史\n                </Button>\n                {currentSubscription.status === SubscriptionStatus.ACTIVE && (\n                  <Button \n                    danger \n                    icon={<StopOutlined />}\n                    onClick={handleCancelSubscription}\n                  >\n                    取消订阅\n                  </Button>\n                )}\n              </Space>\n            </div>\n          </div>\n        ) : (\n          <Empty \n            description=\"暂无有效订阅\"\n            image={Empty.PRESENTED_IMAGE_SIMPLE}\n          >\n            <Button \n              type=\"primary\" \n              icon={<ShoppingCartOutlined />}\n              onClick={() => setSubscribeModalVisible(true)}\n            >\n              立即订阅\n            </Button>\n          </Empty>\n        )}\n      </Card>\n\n      {/* 套餐选择 */}\n      <Card \n        title={\n          <Space>\n            <ShoppingCartOutlined />\n            选择套餐\n          </Space>\n        }\n        loading={plansLoading}\n      >\n        <Row gutter={[16, 16]}>\n          {plans.map((plan) => (\n            <Col xs={24} sm={12} lg={6} key={plan.id}>\n              <Card\n                hoverable\n                className={`plan-card ${currentSubscription?.planId === plan.id ? 'current-plan' : ''}`}\n                actions={[\n                  <Button\n                    key=\"subscribe\"\n                    type={currentSubscription?.planId === plan.id ? 'default' : 'primary'}\n                    disabled={currentSubscription?.planId === plan.id}\n                    onClick={() => {\n                      setSelectedPlan(plan);\n                      setSubscribeModalVisible(true);\n                    }}\n                  >\n                    {currentSubscription?.planId === plan.id ? '当前套餐' : '选择此套餐'}\n                  </Button>\n                ]}\n              >\n                <div style={{ textAlign: 'center' }}>\n                  <Title level={4}>\n                    {plan.name}\n                    {getPlanRecommendation(plan)}\n                  </Title>\n                  <div style={{ fontSize: 32, fontWeight: 'bold', color: '#1890ff' }}>\n                    ¥{plan.price.toFixed(0)}\n                    <span style={{ fontSize: 14, color: '#666' }}>/月</span>\n                  </div>\n                  <Text type=\"secondary\">{plan.description}</Text>\n                </div>\n                \n                <Divider />\n                \n                <List\n                  size=\"small\"\n                  dataSource={getPlanFeatures(plan)}\n                  renderItem={(feature) => (\n                    <List.Item>\n                      <Space>\n                        <CheckOutlined style={{ color: '#52c41a' }} />\n                        {feature}\n                      </Space>\n                    </List.Item>\n                  )}\n                />\n              </Card>\n            </Col>\n          ))}\n        </Row>\n      </Card>\n\n      {/* 订阅确认弹窗 */}\n      <Modal\n        title=\"确认订阅\"\n        open={subscribeModalVisible}\n        onOk={handleSubscribe}\n        onCancel={() => setSubscribeModalVisible(false)}\n        confirmLoading={subscribing}\n        okText=\"确认订阅\"\n        cancelText=\"取消\"\n      >\n        {selectedPlan && (\n          <div>\n            <Alert\n              message={`您选择了 ${selectedPlan.name}`}\n              description={selectedPlan.description}\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <div style={{ marginBottom: 16 }}>\n              <Text strong>订阅时长：</Text>\n              <InputNumber\n                min={1}\n                max={12}\n                value={duration}\n                onChange={(value) => setDuration(value || 1)}\n                addonAfter=\"个月\"\n                style={{ marginLeft: 8 }}\n              />\n            </div>\n            \n            <div>\n              <Text strong>总费用：</Text>\n              <Text style={{ fontSize: 18, color: '#1890ff', marginLeft: 8 }}>\n                ¥{(selectedPlan.price * duration).toFixed(2)}\n              </Text>\n            </div>\n          </div>\n        )}\n      </Modal>\n\n      {/* 订阅历史弹窗 */}\n      <Modal\n        title=\"订阅历史\"\n        open={historyModalVisible}\n        onCancel={() => setHistoryModalVisible(false)}\n        footer={null}\n        width={800}\n      >\n        <Table\n          columns={historyColumns}\n          dataSource={subscriptionHistory}\n          rowKey=\"id\"\n          pagination={{ pageSize: 10 }}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nexport default UnifiedSubscriptionContent;\n", "/**\n * 订阅管理页面 - 统一的订阅管理界面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { message } from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { SubscriptionService } from '@/services';\nimport type { SubscriptionResponse } from '@/types/api';\n\n// 导入统一的订阅管理组件\nimport UnifiedSubscriptionContent from './components/UnifiedSubscriptionContent';\n\nconst SubscriptionPage: React.FC = () => {\n  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionResponse | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchCurrentSubscription();\n  }, []);\n\n  const fetchCurrentSubscription = async () => {\n    try {\n      setLoading(true);\n      const subscription = await SubscriptionService.getCurrentSubscription();\n      setCurrentSubscription(subscription);\n    } catch (error) {\n      console.error('获取当前订阅失败:', error);\n      message.error('获取订阅信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <PageContainer title=\"订阅管理\">\n      <UnifiedSubscriptionContent\n        currentSubscription={currentSubscription}\n        loading={loading}\n        onRefresh={fetchCurrentSubscription}\n      />\n    </PageContainer>\n  );\n};\n\nexport default SubscriptionPage;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BA6cD;;;eAAA;;;;;;wEA3c2C;6BAmBpC;8BAUA;iCAE6B;4BAMD;;;;;;;;;;AAEnC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAQlC,MAAM,6BAAwE,CAAC,EAC7E,mBAAmB,EACnB,OAAO,EACP,SAAS,EACV;;IACC,WAAW;IACX,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAyB,EAAE;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAM;IAChD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAC;IAE/D,WAAW;IACX,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAA6B,EAAE;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAkC;IAClF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IAEzC,IAAA,gBAAS,EAAC;QACR;QACA,IAAI,qBAAqB;YACvB;YACA;QACF;IACF,GAAG;QAAC;KAAoB;IAExB,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,gBAAgB;YAChB,MAAM,YAAY,MAAM,6BAAmB,CAAC,cAAc;YAC1D,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,SAAS;IACT,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,UAAU,MAAM,6BAAmB,CAAC,sBAAsB;YAChE,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,MAAM,6BAAmB,CAAC,YAAY;YACpD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,eAAe;YACf,MAAM,UAAqC;gBACzC,QAAQ,aAAa,EAAE;gBACvB,UAAU;YACZ;YAEA,MAAM,6BAAmB,CAAC,kBAAkB,CAAC;YAC7C,aAAO,CAAC,OAAO,CAAC;YAChB,yBAAyB;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;IACP,MAAM,2BAA2B;QAC/B,IAAI,CAAC,qBAAqB;QAE1B,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,MAAM,6BAAmB,CAAC,kBAAkB,CAAC,oBAAoB,EAAE;oBACnE,aAAO,CAAC,OAAO,CAAC;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,MAAM,eAAe;YACnB,CAAC,uBAAkB,CAAC,MAAM,CAAC,EAAE;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAC1D,CAAC,uBAAkB,CAAC,OAAO,CAAC,EAAE;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC1D,CAAC,uBAAkB,CAAC,QAAQ,CAAC,EAAE;gBAAE,OAAO;gBAAW,MAAM;YAAM;YAC/D,CAAC,uBAAkB,CAAC,OAAO,CAAC,EAAE;gBAAE,OAAO;gBAAU,MAAM;YAAM;QAC/D;QAEA,MAAM,SAAS,YAAY,CAAC,OAAO,IAAI;YAAE,OAAO;YAAW,MAAM;QAAK;QACtE,qBAAO,2BAAC,SAAG;YAAC,OAAO,OAAO,KAAK;sBAAG,OAAO,IAAI;;;;;;IAC/C;IAEA,WAAW;IACX,MAAM,wBAAwB,CAAC;QAC7B,IAAI,KAAK,IAAI,KAAK,OAChB,qBAAO,2BAAC,SAAG;YAAC,OAAM;YAAS,oBAAM,2BAAC,mBAAY;;;;;sBAAK;;;;;;QAErD,IAAI,KAAK,IAAI,KAAK,OAChB,qBAAO,2BAAC,SAAG;YAAC,OAAM;YAAO,oBAAM,2BAAC,oBAAa;;;;;sBAAK;;;;;;QAEpD,OAAO;IACT;IAEA,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW;YACf,CAAC,IAAI,EAAE,KAAK,OAAO,KAAK,SAAS,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC;YAC1D;YACA;YACA;SACD;QAED,IAAI,KAAK,IAAI,KAAK,OAChB,SAAS,IAAI,CAAC;QAEhB,IAAI,KAAK,IAAI,KAAK,OAAO;YACvB,SAAS,IAAI,CAAC;YACd,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,iBAAoD;QACxD;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,SAA+B,aAAa;QACvD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,OAAO,IAAI,KAAK,MAAM,kBAAkB,KAAK;QACzE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,QAAkB,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC;QACnD;KACD;IAED,qBACE,2BAAC;;0BAEC,2BAAC,UAAI;gBACH,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,oBAAa;;;;;wBAAG;;;;;;;gBAIrB,qBACE,2BAAC,YAAM;oBACL,oBAAM,2BAAC,qBAAc;;;;;oBACrB,SAAS;oBACT,SAAS;8BACV;;;;;;gBAIH,OAAO;oBAAE,cAAc;gBAAG;0BAEzB,oCACC,2BAAC;;sCACC,2BAAC,kBAAY;4BAAC,QAAQ;4BAAG,QAAQ;;8CAC/B,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACvB,cAAA,2BAAC,WAAK;;4CACH,oBAAoB,QAAQ;4CAC5B,aAAa,oBAAoB,MAAM;;;;;;;;;;;;8CAG5C,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,oBAAoB,OAAO,KAAK,SAAS,QAAQ,CAAC,EAAE,oBAAoB,OAAO,CAAC,EAAE,CAAC;;;;;;8CAEtF,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,IAAI,KAAK,oBAAoB,SAAS,EAAE,kBAAkB;;;;;;8CAE7D,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,oBAAoB,OAAO,GACxB,IAAI,KAAK,oBAAoB,OAAO,EAAE,kBAAkB,KACxD;;;;;;8CAGN,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;;wCAAK;wCAC1B,oBAAoB,KAAK,CAAC,OAAO,CAAC;;;;;;;8CAEtC,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,CAAA,sBAAA,gCAAA,UAAW,aAAa,MAAK,YAC1B,CAAC,EAAE,UAAU,aAAa,CAAC,EAAE,CAAC,GAC9B;;;;;;;;;;;;wBAKP,2BACC,2BAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAG;;8CAC1B,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC,cAAQ;oCACP,SAAS,UAAU,eAAe;oCAClC,QAAQ,IAAM,CAAC,EAAE,UAAU,YAAY,CAAC,CAAC,EAAE,UAAU,QAAQ,KAAK,SAAS,MAAM,UAAU,QAAQ,CAAC,CAAC;oCACrG,OAAO;wCAAE,WAAW;oCAAE;;;;;;;;;;;;sCAK5B,2BAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAG;sCAC1B,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,iBAAU;;;;;wCACjB,SAAS,IAAM,yBAAyB;kDACzC;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,sBAAe;;;;;wCACtB,SAAS,IAAM,uBAAuB;kDACvC;;;;;;oCAGA,oBAAoB,MAAM,KAAK,uBAAkB,CAAC,MAAM,kBACvD,2BAAC,YAAM;wCACL,MAAM;wCACN,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;yCAQT,2BAAC,WAAK;oBACJ,aAAY;oBACZ,OAAO,WAAK,CAAC,sBAAsB;8BAEnC,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,oBAAM,2BAAC,2BAAoB;;;;;wBAC3B,SAAS,IAAM,yBAAyB;kCACzC;;;;;;;;;;;;;;;;0BAQP,2BAAC,UAAI;gBACH,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,2BAAoB;;;;;wBAAG;;;;;;;gBAI5B,SAAS;0BAET,cAAA,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;8BAClB,MAAM,GAAG,CAAC,CAAC,qBACV,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,2BAAC,UAAI;gCACH,SAAS;gCACT,WAAW,CAAC,UAAU,EAAE,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE,GAAG,iBAAiB,GAAG,CAAC;gCACvF,SAAS;kDACP,2BAAC,YAAM;wCAEL,MAAM,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE,GAAG,YAAY;wCAC5D,UAAU,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE;wCACjD,SAAS;4CACP,gBAAgB;4CAChB,yBAAyB;wCAC3B;kDAEC,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE,GAAG,SAAS;uCARhD;;;;;iCAUP;;kDAED,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAS;;0DAChC,2BAAC;gDAAM,OAAO;;oDACX,KAAK,IAAI;oDACT,sBAAsB;;;;;;;0DAEzB,2BAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAI,YAAY;oDAAQ,OAAO;gDAAU;;oDAAG;oDAChE,KAAK,KAAK,CAAC,OAAO,CAAC;kEACrB,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;wDAAO;kEAAG;;;;;;;;;;;;0DAEhD,2BAAC;gDAAK,MAAK;0DAAa,KAAK,WAAW;;;;;;;;;;;;kDAG1C,2BAAC,aAAO;;;;;kDAER,2BAAC,UAAI;wCACH,MAAK;wCACL,YAAY,gBAAgB;wCAC5B,YAAY,CAAC,wBACX,2BAAC,UAAI,CAAC,IAAI;0DACR,cAAA,2BAAC,WAAK;;sEACJ,2BAAC,oBAAa;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;wDACxC;;;;;;;;;;;;;;;;;;;;;;;2BAvCoB,KAAK,EAAE;;;;;;;;;;;;;;;0BAmD9C,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU,IAAM,yBAAyB;gBACzC,gBAAgB;gBAChB,QAAO;gBACP,YAAW;0BAEV,8BACC,2BAAC;;sCACC,2BAAC,WAAK;4BACJ,SAAS,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC,CAAC;4BACpC,aAAa,aAAa,WAAW;4BACrC,MAAK;4BACL,OAAO;gCAAE,cAAc;4BAAG;;;;;;sCAG5B,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CAC7B,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC,iBAAW;oCACV,KAAK;oCACL,KAAK;oCACL,OAAO;oCACP,UAAU,CAAC,QAAU,YAAY,SAAS;oCAC1C,YAAW;oCACX,OAAO;wCAAE,YAAY;oCAAE;;;;;;;;;;;;sCAI3B,2BAAC;;8CACC,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC;oCAAK,OAAO;wCAAE,UAAU;wCAAI,OAAO;wCAAW,YAAY;oCAAE;;wCAAG;wCAC3D,CAAA,aAAa,KAAK,GAAG,QAAO,EAAG,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,uBAAuB;gBACvC,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,WAAK;oBACJ,SAAS;oBACT,YAAY;oBACZ,QAAO;oBACP,YAAY;wBAAE,UAAU;oBAAG;;;;;;;;;;;;;;;;;AAKrC;GA1ZM;KAAA;IA4ZN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AChdf;;CAEC;;;;4BA2CD;;;eAAA;;;;;;;wEAzC2C;6BACnB;sCACM;iCACM;4FAIG;;;;;;;;;;AAEvC,MAAM,mBAA6B;;IACjC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAA8B;IAC5F,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IAEvC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,2BAA2B;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,eAAe,MAAM,6BAAmB,CAAC,sBAAsB;YACrE,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,mCAA0B;YACzB,qBAAqB;YACrB,SAAS;YACT,WAAW;;;;;;;;;;;AAInB;GA9BM;KAAA;IAgCN,WAAe"}