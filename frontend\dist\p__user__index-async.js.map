{"version": 3, "sources": ["src/pages/user/components/UserProfileContent.tsx", "src/pages/user/index.tsx"], "sourcesContent": ["/**\n * 用户资料内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Button,\n  Space,\n  Typography,\n  message,\n  Avatar,\n  Upload\n} from 'antd';\nimport {\n  UserOutlined,\n  EditOutlined,\n  MailOutlined,\n  SaveOutlined,\n  UploadOutlined\n} from '@ant-design/icons';\nimport { UserService } from '@/services';\nimport type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst UserProfileContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [editing, setEditing] = useState(false);\n  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchUserProfile();\n  }, []);\n\n  const fetchUserProfile = async () => {\n    try {\n      setLoading(true);\n      const profile = await UserService.getUserProfile();\n      setUserProfile(profile);\n      form.setFieldsValue({\n        name: profile.name,\n        email: profile.email,\n      });\n    } catch (error) {\n      console.error('获取用户资料失败:', error);\n      message.error('获取用户资料失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSaveProfile = async (values: any) => {\n    try {\n      setSaving(true);\n      const updateData: UpdateUserProfileRequest = {\n        name: values.name,\n      };\n\n      const updatedProfile = await UserService.updateUserProfile(updateData);\n      setUserProfile(updatedProfile);\n      setEditing(false);\n      message.success('个人资料更新成功');\n    } catch (error) {\n      console.error('更新个人资料失败:', error);\n      message.error('更新个人资料失败');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setEditing(false);\n    if (userProfile) {\n      form.setFieldsValue({\n        name: userProfile.name,\n        email: userProfile.email,\n      });\n    }\n  };\n\n  if (loading || !userProfile) {\n    return <div>加载中...</div>;\n  }\n\n  return (\n    <div>\n      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24 }}>\n        {/* 头像部分 */}\n        <div style={{ textAlign: 'center' }}>\n          <Avatar size={120} icon={<UserOutlined />} />\n          <div style={{ marginTop: 16 }}>\n            <Upload\n              showUploadList={false}\n              beforeUpload={() => {\n                message.info('头像上传功能暂未实现');\n                return false;\n              }}\n            >\n              <Button icon={<UploadOutlined />} size=\"small\">\n                更换头像\n              </Button>\n            </Upload>\n          </div>\n        </div>\n\n        {/* 表单部分 */}\n        <div style={{ flex: 1 }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n            <Title level={4} style={{ margin: 0 }}>\n              <UserOutlined /> 基本信息\n            </Title>\n            {!editing && (\n              <Button\n                type=\"primary\"\n                icon={<EditOutlined />}\n                onClick={() => setEditing(true)}\n              >\n                编辑资料\n              </Button>\n            )}\n          </div>\n\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleSaveProfile}\n            disabled={!editing}\n          >\n            <Form.Item\n              label=\"用户名\"\n              name=\"name\"\n              rules={[\n                { required: true, message: '请输入用户名' },\n                { max: 100, message: '用户名不能超过100个字符' }\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"请输入用户名\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"邮箱地址\"\n              name=\"email\"\n            >\n              <Input\n                prefix={<MailOutlined />}\n                disabled\n                placeholder=\"邮箱地址不可修改\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Space>\n                {editing ? (\n                  <>\n                    <Button\n                      type=\"primary\"\n                      htmlType=\"submit\"\n                      loading={saving}\n                      icon={<SaveOutlined />}\n                    >\n                      保存修改\n                    </Button>\n                    <Button onClick={handleCancel}>\n                      取消\n                    </Button>\n                  </>\n                ) : (\n                  <Button\n                    type=\"primary\"\n                    icon={<EditOutlined />}\n                    onClick={() => setEditing(true)}\n                  >\n                    编辑资料\n                  </Button>\n                )}\n              </Space>\n            </Form.Item>\n          </Form>\n        </div>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default UserProfileContent;\n", "/**\n * 用户管理页面 - 个人资料管理\n */\n\nimport React from 'react';\nimport {\n  Card,\n  Typography\n} from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\n\n// 导入原有的组件内容\nimport UserProfileContent from './components/UserProfileContent';\n\nconst { Title } = Typography;\n\nconst UserManagePage: React.FC = () => {\n  return (\n    <PageContainer title=\"用户管理\">\n      <Card>\n        <UserProfileContent />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default UserManagePage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA+LD;;;eAAA;;;;;;wEA7L2C;6BAUpC;8BAOA;iCACqB;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,qBAA+B;;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA6B;IAC3E,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,UAAU,MAAM,qBAAW,CAAC,cAAc;YAChD,eAAe;YACf,KAAK,cAAc,CAAC;gBAClB,MAAM,QAAQ,IAAI;gBAClB,OAAO,QAAQ,KAAK;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,UAAU;YACV,MAAM,aAAuC;gBAC3C,MAAM,OAAO,IAAI;YACnB;YAEA,MAAM,iBAAiB,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YAC3D,eAAe;YACf,WAAW;YACX,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,IAAI,aACF,KAAK,cAAc,CAAC;YAClB,MAAM,YAAY,IAAI;YACtB,OAAO,YAAY,KAAK;QAC1B;IAEJ;IAEA,IAAI,WAAW,CAAC,aACd,qBAAO,2BAAC;kBAAI;;;;;;IAGd,qBACE,2BAAC;kBACC,cAAA,2BAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAc,KAAK;YAAG;;8BAE/D,2BAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAS;;sCAChC,2BAAC,YAAM;4BAAC,MAAM;4BAAK,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCACtC,2BAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAG;sCAC1B,cAAA,2BAAC,YAAM;gCACL,gBAAgB;gCAChB,cAAc;oCACZ,aAAO,CAAC,IAAI,CAAC;oCACb,OAAO;gCACT;0CAEA,cAAA,2BAAC,YAAM;oCAAC,oBAAM,2BAAC,qBAAc;;;;;oCAAK,MAAK;8CAAQ;;;;;;;;;;;;;;;;;;;;;;8BAQrD,2BAAC;oBAAI,OAAO;wBAAE,MAAM;oBAAE;;sCACpB,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAiB,YAAY;gCAAU,cAAc;4BAAG;;8CACrG,2BAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,QAAQ;oCAAE;;sDAClC,2BAAC,mBAAY;;;;;wCAAG;;;;;;;gCAEjB,CAAC,yBACA,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,mBAAY;;;;;oCACnB,SAAS,IAAM,WAAW;8CAC3B;;;;;;;;;;;;sCAML,2BAAC,UAAI;4BACH,MAAM;4BACN,QAAO;4BACP,UAAU;4BACV,UAAU,CAAC;;8CAEX,2BAAC,UAAI,CAAC,IAAI;oCACR,OAAM;oCACN,MAAK;oCACL,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAS;wCACpC;4CAAE,KAAK;4CAAK,SAAS;wCAAgB;qCACtC;8CAED,cAAA,2BAAC,WAAK;wCACJ,sBAAQ,2BAAC,mBAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,2BAAC,UAAI,CAAC,IAAI;oCACR,OAAM;oCACN,MAAK;8CAEL,cAAA,2BAAC,WAAK;wCACJ,sBAAQ,2BAAC,mBAAY;;;;;wCACrB,QAAQ;wCACR,aAAY;;;;;;;;;;;8CAIhB,2BAAC,UAAI,CAAC,IAAI;8CACR,cAAA,2BAAC,WAAK;kDACH,wBACC;;8DACE,2BAAC,YAAM;oDACL,MAAK;oDACL,UAAS;oDACT,SAAS;oDACT,oBAAM,2BAAC,mBAAY;;;;;8DACpB;;;;;;8DAGD,2BAAC,YAAM;oDAAC,SAAS;8DAAc;;;;;;;yEAKjC,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS,IAAM,WAAW;sDAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAanB;GApKM;;QAKW,UAAI,CAAC;;;KALhB;IAsKN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjMf;;CAEC;;;;4BAwBD;;;eAAA;;;;;;;uEAtBkB;6BAIX;sCACuB;oFAGC;;;;;;;;;AAE/B,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAU;AAE5B,MAAM,iBAA2B;IAC/B,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,2BAAkB;;;;;;;;;;;;;;;AAI3B;KARM;IAUN,WAAe"}