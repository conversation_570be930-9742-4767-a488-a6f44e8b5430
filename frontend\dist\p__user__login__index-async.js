((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__user__login__index'],
{ "src/pages/user/login/index.tsx": function (module, exports, __mako_require__){
/**
 * 登录页面
 * 实现双阶段认证的第一阶段：账号登录
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _components = __mako_require__("src/components/index.ts");
var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
            backgroundSize: '100% 100%'
        },
        content: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '32px 16px'
        },
        header: {
            marginBottom: 40,
            textAlign: 'center'
        },
        logo: {
            marginBottom: 16
        },
        title: {
            marginBottom: 0
        },
        loginCard: {
            width: '100%',
            maxWidth: 400,
            boxShadow: token.boxShadowTertiary
        },
        footer: {
            marginTop: 40,
            textAlign: 'center'
        },
        lang: {
            width: 42,
            height: 42,
            lineHeight: '42px',
            position: 'fixed',
            right: 16,
            top: 16,
            borderRadius: token.borderRadius,
            ':hover': {
                backgroundColor: token.colorBgTextHover
            }
        }
    };
});
const LoginPage = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [activeTab, setActiveTab] = (0, _react.useState)('login');
    const { styles } = useStyles();
    const { setInitialState } = (0, _max.useModel)('@@initialState');
    // 自定义邮箱验证函数
    const validateEmail = (email)=>{
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };
    // 处理登录
    const handleLogin = async (values)=>{
        setLoading(true);
        try {
            const response = await _services.AuthService.login(values);
            _antd.message.success('登录成功！');
            // 登录成功后，刷新 initialState
            await setInitialState((prevState)=>({
                    ...prevState,
                    currentUser: response.user,
                    currentTeam: response.teams.length > 0 ? response.teams[0] : undefined
                }));
            // 根据团队数量进行不同的跳转处理
            if (response.teams.length === 0) // 没有团队，跳转到创建团队页面
            _max.history.push('/team/create');
            else // 有团队（无论一个还是多个），都跳转到个人中心整合页面
            _max.history.push('/personal-center', {
                teams: response.teams
            });
        } catch (error) {
            console.error('登录失败:', error);
        } finally{
            setLoading(false);
        }
    };
    // 处理注册
    const handleRegister = async (values)=>{
        setLoading(true);
        try {
            await _services.AuthService.register(values);
            _antd.message.success('注册成功！请登录');
            setActiveTab('login');
        } catch (error) {
            console.error('注册失败:', error);
        } finally{
            setLoading(false);
        }
    };
    // 登录表单
    const LoginForm = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            name: "login",
            size: "large",
            onFinish: handleLogin,
            autoComplete: "off",
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "email",
                    rules: [
                        {
                            required: true,
                            message: '请输入邮箱！'
                        },
                        {
                            validator: (_, value)=>{
                                if (!value || validateEmail(value)) return Promise.resolve();
                                return Promise.reject(new Error('请输入有效的邮箱地址！'));
                            }
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 151,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "邮箱",
                        autoComplete: "email"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 150,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 136,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "password",
                    rules: [
                        {
                            required: true,
                            message: '请输入密码！'
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LockOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 164,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "密码",
                        autoComplete: "current-password"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 163,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 157,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        htmlType: "submit",
                        loading: loading,
                        block: true,
                        children: "登录"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 171,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 170,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/user/login/index.tsx",
            lineNumber: 130,
            columnNumber: 5
        }, this);
    // 注册表单
    const RegisterForm = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            name: "register",
            size: "large",
            onFinish: handleRegister,
            autoComplete: "off",
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "name",
                    rules: [
                        {
                            required: true,
                            message: '请输入用户名！'
                        },
                        {
                            max: 100,
                            message: '用户名长度不能超过100字符！'
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 199,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "用户名",
                        autoComplete: "name"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 198,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 191,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "email",
                    rules: [
                        {
                            required: true,
                            message: '请输入邮箱！'
                        },
                        {
                            validator: (_, value)=>{
                                if (!value || validateEmail(value)) return Promise.resolve();
                                return Promise.reject(new Error('请输入有效的邮箱地址！'));
                            }
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 220,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "邮箱",
                        autoComplete: "email"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 219,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 205,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "password",
                    rules: [
                        {
                            required: true,
                            message: '请输入密码！'
                        },
                        {
                            min: 8,
                            message: '密码长度至少8位！'
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LockOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 234,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "密码（至少8位）",
                        autoComplete: "new-password"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 233,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 226,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "confirmPassword",
                    dependencies: [
                        'password'
                    ],
                    rules: [
                        {
                            required: true,
                            message: '请确认密码！'
                        },
                        ({ getFieldValue })=>({
                                validator (_, value) {
                                    if (!value || getFieldValue('password') === value) return Promise.resolve();
                                    return Promise.reject(new Error('两次输入的密码不一致！'));
                                }
                            })
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LockOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 256,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "确认密码",
                        autoComplete: "new-password"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 255,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 240,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        htmlType: "submit",
                        loading: loading,
                        block: true,
                        children: "注册"
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 263,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 262,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/user/login/index.tsx",
            lineNumber: 185,
            columnNumber: 5
        }, this);
    const tabItems = [
        {
            key: 'login',
            label: '登录',
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(LoginForm, {}, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 279,
                columnNumber: 17
            }, this)
        },
        {
            key: 'register',
            label: '注册',
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(RegisterForm, {}, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 284,
                columnNumber: 17
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "登录页",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 291,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 290,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.header,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            align: "center",
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.logo,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                        src: "/logo.svg",
                                        alt: "TeamAuth",
                                        height: 48
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 300,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 299,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.title,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 2,
                                            children: "团队管理系统"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 303,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "现代化的团队协作与管理平台"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 304,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 302,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 298,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 297,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        className: styles.loginCard,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: setActiveTab,
                            centered: true,
                            items: tabItems
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 310,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 309,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.footer,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "© 2025 TeamAuth. All rights reserved."
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 319,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 318,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 296,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 324,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/login/index.tsx",
        lineNumber: 289,
        columnNumber: 5
    }, this);
};
_s(LoginPage, "Fd+o7ZZlz516hVujTDENm/ww8fo=", false, function() {
    return [
        useStyles,
        _max.useModel
    ];
});
_c = LoginPage;
var _default = LoginPage;
var _c;
$RefreshReg$(_c, "LoginPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__user__login__index-async.js.map