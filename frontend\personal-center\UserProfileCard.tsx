import {
  CalendarOutlined,
  CarOutlined,
  MailOutlined,
  PhoneOutlined,
  TeamOutlined,
  UserOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Card,
  Col,
  Flex,
  Row,
  Space,
  Tag,
  Typography,
} from "antd";
import React from "react";

const { Title, Text } = Typography;

const UserProfileCard: React.FC = () => {
  // 个人信息数据
  const userInfo = {
    name: "张明",
    position: "车队管理员",
    email: "<EMAIL>",
    phone: "13800138000",
    registerDate: "2020年5月10日",
    lastLoginTime: "2025年7月25日 18:30:45",
    lastLoginTeam: "运输车队管理员",
    teamCount: 8, // 新增团队总数
  };

  // 个人统计数据
  const personalStats = {
    vehicles: 48,
    personnel: 16,
    warnings: 5,
    alerts: 3,
  };



  // 状态管理

  return (
    <Card
        className="dashboard-card"
        style={{
          borderRadius: 12,
          boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
          border: "none",
          background: "linear-gradient(145deg, #ffffff, #f0f7ff)",
          position: "relative",
        }}
      >


        {/* 三列布局 */}
        <Row gutter={0} style={{ margin: 0 }}>
          {/* 第一列：姓名、头像和邮箱等信息 */}
          <Col xs={24} sm={8}>
            <Flex align="center">
              <Avatar
                size={52}
                style={{
                  backgroundColor: "#1890ff",
                  fontSize: 18,
                  boxShadow: "0 0 0 3px rgba(24, 144, 255, 0.2)",
                  marginRight: 10,
                }}
              >
                {userInfo.name.substring(0, 1)}
              </Avatar>

              <Space direction="vertical" size={0}>
                <Flex align="center" style={{ marginBottom: 4 }}>
                  <Title level={4} style={{ margin: 0, fontSize: 16 }}>
                    {userInfo.name}
                  </Title>
                  <Tooltip title="最后登录时间">
                    <Text
                      type="secondary"
                      style={{ marginLeft: 8, fontSize: 11 }}
                    >
                      <CalendarOutlined style={{ marginRight: 2 }} />
                      {userInfo.lastLoginTime}
                    </Text>
                  </Tooltip>
                </Flex>
                {/* 邮箱、电话和注册日期信息 */}
                <Flex wrap="wrap" gap={6}>
                  <Tag
                    icon={<MailOutlined style={{ fontSize: 12 }} />}
                    style={{ fontSize: 13 }}
                  >
                    {userInfo.email}
                  </Tag>
                  <Tag
                    icon={<PhoneOutlined style={{ fontSize: 12 }} />}
                    style={{ fontSize: 13 }}
                  >
                    {userInfo.phone}
                  </Tag>
                  <Tag
                    icon={<CalendarOutlined style={{ fontSize: 12 }} />}
                    style={{ fontSize: 13 }}
                  >
                    {userInfo.registerDate}
                  </Tag>
                </Flex>
              </Space>
            </Flex>
          </Col>

          {/* 第二列：车辆人员预警告警统计 */}
          <Col xs={24} sm={10}>
            <Row gutter={8} justify="space-around">
              {/* 车辆统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <CarOutlined />
                    <Text style={{ marginLeft: 4 }}>车辆</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#1890ff",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.vehicles}
                  </Text>
                </Space>
              </Col>

              {/* 人员统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <UserOutlined />
                    <Text style={{ marginLeft: 4 }}>人员</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#52c41a",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.personnel}
                  </Text>
                </Space>
              </Col>

              {/* 预警统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <WarningOutlined />
                    <Text style={{ marginLeft: 4 }}>预警</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#faad14",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.warnings}
                  </Text>
                </Space>
              </Col>

              {/* 告警统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <WarningOutlined style={{ color: "#ff4d4f" }} />
                    <Text style={{ marginLeft: 4 }}>告警</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#ff4d4f",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.alerts}
                  </Text>
                </Space>
              </Col>
            </Row>
          </Col>

          {/* 第三列：最后登录信息 */}
          <Col xs={24} sm={6}>
            <Space direction="vertical">
              <Flex align="center">
                <TeamOutlined
                  style={{ color: "#8c8c8c", marginRight: 6, fontSize: 12 }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  最后登录团队: <Text strong>{userInfo.lastLoginTeam}</Text>
                </Text>
              </Flex>
              <Flex align="center">
                <TeamOutlined
                  style={{ color: "#8c8c8c", marginRight: 6, fontSize: 12 }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  团队总数: <Text strong>{userInfo.teamCount}</Text>
                </Text>
              </Flex>
            </Space>
          </Col>
        </Row>
    </Card>
  );
};

export default UserProfileCard;
