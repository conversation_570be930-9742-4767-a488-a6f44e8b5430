// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"name":"team-select","path":"/user/team-select","redirect":"/personal-center","parentId":"1","id":"3"},"4":{"path":"/dashboard","name":"仪表盘","icon":"dashboard","parentId":"ant-design-pro-layout","id":"4"},"5":{"path":"/team","name":"团队管理","icon":"team","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"5"},"6":{"path":"/team","redirect":"/team/list","parentId":"5","id":"6"},"7":{"path":"/team/list","parentId":"5","id":"7"},"8":{"path":"/team/detail","parentId":"5","id":"8"},"9":{"path":"/personal-center","name":"个人中心","icon":"user","layout":false,"id":"9"},"10":{"path":"/user-manage","name":"用户管理","icon":"user","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"10"},"11":{"path":"/subscription","name":"订阅管理","icon":"crown","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"11"},"12":{"path":"/friend","name":"好友管理","icon":"userAdd","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"12"},"13":{"path":"/help","name":"帮助中心","icon":"question","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"13"},"14":{"path":"/","redirect":"/dashboard","parentId":"ant-design-pro-layout","id":"14"},"15":{"path":"*","layout":false,"id":"15"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__user__login__index" */'@/pages/user/login/index.tsx')),
'3': React.lazy(() => import('./EmptyRoute')),
'4': React.lazy(() => import(/* webpackChunkName: "p__Dashboard__index" */'@/pages/Dashboard/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__team__index" */'@/pages/team/index.tsx')),
'6': React.lazy(() => import('./EmptyRoute')),
'7': React.lazy(() => import(/* webpackChunkName: "p__team__index" */'@/pages/team/index.tsx')),
'8': React.lazy(() => import(/* webpackChunkName: "p__team__detail__index" */'@/pages/team/detail/index.tsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__personal-center__index" */'@/pages/personal-center/index.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__user__index" */'@/pages/user/index.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__subscription__index" */'@/pages/subscription/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__friend__index" */'@/pages/friend/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__help__index" */'@/pages/help/index.tsx')),
'14': React.lazy(() => import('./EmptyRoute')),
'15': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "t__plugin-layout__Layout" */'F:/Project/teamAuth/frontend/src/.umi-production/plugin-layout/Layout.tsx')),
},
  };
}
