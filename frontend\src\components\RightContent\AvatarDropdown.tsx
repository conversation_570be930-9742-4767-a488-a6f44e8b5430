import {
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Spin, Avatar, Typography } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';
import HeaderDropdown from '../HeaderDropdown';

const { Text } = Typography;

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon">{currentUser?.name}</span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    dropdownItem: {
      display: 'flex',
      alignItems: 'center',
      gap: 8,
      padding: '8px 12px',
      minWidth: 200,
    },
  };
});

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({
  menu,
  children,
}) => {
  const { styles } = useStyles();
  const { initialState } = useModel('@@initialState');

  const onMenuClick: MenuProps['onClick'] = (event) => {
    const { key } = event;

    // 处理菜单项点击
    if (key === 'profile') {
      history.push('/personal-center');
    } else if (key === 'settings') {
      // 可以跳转到设置页面或打开设置模态框
      console.log('打开设置');
    } else if (key === 'logout') {
      // 执行注销逻辑
      console.log('执行注销');
      // 这里可以调用注销API和清理状态
    }
  };

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  // 构建菜单项 - 包含个人信息、设置和注销选项
  const buildMenuItems = () => {
    const items = [];

    if (menu) {
      // 个人信息选项
      items.push({
        key: 'profile',
        icon: <UserOutlined />,
        label: '个人信息',
      });

      // 设置选项
      items.push({
        key: 'settings',
        icon: <SettingOutlined />,
        label: '设置',
      });

      // 分隔线
      items.push({
        type: 'divider',
      });

      // 注销选项
      items.push({
        key: 'logout',
        icon: <LogoutOutlined />,
        label: '退出登录',
        danger: true,
      });
    }

    return items;
  };

  const menuItems = buildMenuItems();

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      {children}
    </HeaderDropdown>
  );
};
