import React from 'react';
import { Card, Typography, Space } from 'antd';
import TeamListCard from './TeamListCard';

const { Title, Paragraph } = Typography;

/**
 * 团队列表卡片组件演示页面
 * 用于展示优化后的UI设计效果
 */
const TeamListCardDemo: React.FC = () => {
  return (
    <div style={{ 
      padding: '24px', 
      background: '#f5f5f5', 
      minHeight: '100vh' 
    }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card style={{ borderRadius: 12 }}>
          <Title level={2}>团队列表卡片组件 - UI优化演示</Title>
          <Paragraph>
            本演示展示了团队列表卡片组件的UI优化效果，包括：
          </Paragraph>
          <ul>
            <li><strong>团队角色与身份显示优化</strong>：重新设计了角色标签的视觉呈现，使用渐变色和图标来区分管理员和成员身份</li>
            <li><strong>进入团队的交互方式改进</strong>：优化了团队切换的交互设计，添加了hover效果和更直观的视觉反馈</li>
            <li><strong>指标卡片布局重新设计</strong>：采用网格布局重新排列指标卡片，提升了数据的视觉层次和可读性</li>
            <li><strong>响应式设计</strong>：确保在不同屏幕尺寸下都有良好的显示效果</li>
            <li><strong>移除上次登录时间</strong>：简化了信息显示，专注于核心数据</li>
          </ul>
        </Card>

        <TeamListCard />

        <Card style={{ borderRadius: 12 }}>
          <Title level={3}>设计特点</Title>
          <Space direction="vertical" size="middle">
            <div>
              <Title level={4}>🎨 视觉设计</Title>
              <Paragraph>
                • 使用渐变色背景和阴影效果提升视觉层次<br/>
                • 当前团队有特殊的视觉标识和装饰性背景<br/>
                • 角色标签采用不同颜色的渐变设计（管理员：紫色，成员：绿色）
              </Paragraph>
            </div>
            
            <div>
              <Title level={4}>🖱️ 交互体验</Title>
              <Paragraph>
                • 团队名称hover时显示切换提示图标<br/>
                • 指标卡片支持hover动画效果<br/>
                • 当前团队卡片有特殊的视觉状态<br/>
                • 切换过程中显示加载状态
              </Paragraph>
            </div>
            
            <div>
              <Title level={4}>📱 响应式布局</Title>
              <Paragraph>
                • 在移动设备上指标卡片自动调整为单列布局<br/>
                • 平板设备上保持2列网格布局<br/>
                • 桌面设备上显示完整的2x2网格布局
              </Paragraph>
            </div>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default TeamListCardDemo;
