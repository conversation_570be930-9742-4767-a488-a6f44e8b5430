import {
  Bar<PERSON>hartOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  Card,
  Flex,
  Space,
  Typography,
  Spin,
  Alert,
  Avatar,
  Row,
  Col,
} from "antd";
import React, { useState, useEffect } from "react";
import { UserService } from "@/services/user";
import type { UserPersonalStatsResponse, UserProfileDetailResponse } from "@/types/api";
 
const { Title, Text } = Typography;

const UserProfileCard: React.FC = () => {
  // 用户详细信息状态
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: "",
    position: "",
    email: "",
    telephone: "",
    registerDate: "",
    lastLoginTime: "",
    lastLoginTeam: "",
    teamCount: 0,
    avatar: "",
  });
  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 个人统计数据状态
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);



  // 状态管理

  // 获取用户数据
  useEffect(() => {
    console.log('UserProfileCard: useEffect 开始执行');

    const fetchUserData = async () => {
      try {
        console.log('UserProfileCard: 开始获取用户数据');

        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个
        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {
          console.error('获取用户详细信息失败:', error);
          setUserInfoError('获取用户详细信息失败，请稍后重试');
          return null;
        });

        const statsPromise = UserService.getUserPersonalStats().catch(error => {
          console.error('获取统计数据失败:', error);
          setStatsError('获取统计数据失败，请稍后重试');
          return null;
        });

        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);

        if (userDetail) {
          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
          setUserInfo(userDetail);
          setUserInfoError(null);
        }

        if (stats) {
          console.log('UserProfileCard: 获取到统计数据:', stats);
          setPersonalStats(stats);
          setStatsError(null);
        }

      } catch (error) {
        console.error('获取用户数据时发生未知错误:', error);
        setUserInfoError('获取用户数据失败，请刷新页面重试');
        setStatsError('获取统计数据失败，请刷新页面重试');
      } finally {
        setUserInfoLoading(false);
        setStatsLoading(false);
      }
    };

    fetchUserData();
  }, []);



  return (
    <>
      {/* 用户信息主卡片 */}
      {userInfoError ? (
        <Alert
          message="用户信息加载失败"
          description={userInfoError}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      ) : (
        <Spin spinning={userInfoLoading}>
          {/* 使用 Card 组件替代自定义 div */}
          <Card
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              borderRadius: 16,
              color: "white",
              position: "relative",
              overflow: "hidden",
              minHeight: 140, // 改为最小高度，允许内容撑开
              border: "none",
            }}
            styles={{
              body: {
                padding: "16px 16px 16px 16px", // 统一使用16px内边距
                height: "100%"
              }
            }}
          >

              {/* 装饰性背景元素 */}
              <div
                style={{
                  position: "absolute",
                  top: -25,
                  right: -25,
                  width: 100,
                  height: 100,
                  background: "rgba(255,255,255,0.1)",
                  borderRadius: "50%",
                }}
              />
              <div
                style={{
                  position: "absolute",
                  bottom: -30,
                  left: -30,
                  width: 80,
                  height: 80,
                  background: "rgba(255,255,255,0.05)",
                  borderRadius: "50%",
                }}
              />
              <div
                style={{
                  position: "absolute",
                  top: "50%",
                  right: "20%",
                  width: 60,
                  height: 60,
                  background: "rgba(255,255,255,0.03)",
                  borderRadius: "50%",
                  transform: "translateY(-50%)",
                }}
              />

              {/* 主要内容区域 - 使用响应式网格布局 */}
              <Row
                gutter={[16, 12]}
                align="middle"
                style={{ position: "relative", zIndex: 1, width: "100%", minHeight: "100%" }}
              >
                {/* 第一列：用户基本信息区域 */}
                <Col xs={24} sm={24} md={8} lg={7} xl={6}>
                  <Flex align="center" style={{ minHeight: "80px" }}>
                    {/* 用户头像 - 使用 Ant Design Avatar 组件 */}
                    <Avatar
                      size={64}
                      shape="square"
                      style={{
                        backgroundColor: "rgba(255,255,255,0.2)",
                        marginRight: 20,
                        fontSize: 24,
                        fontWeight: 600,
                        border: "2px solid rgba(255,255,255,0.3)",
                      }}
                    >
                      {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : <UserOutlined />}
                    </Avatar>

                    {/* 用户信息 - 使用 Space 组件垂直布局 */}
                    <Space direction="vertical" size={4}>
                      <Title
                        level={3}
                        style={{
                          margin: 0,
                          color: "white",
                          fontSize: 22,
                          fontWeight: 600,
                        }}
                      >
                        {userInfo.name || "加载中..."}
                      </Title>

                      {/* 联系信息 - 使用 Space 组件垂直排列 */}
                      <Space direction="vertical" size={4}>
                        {userInfo.email && (
                          <Space size={6} align="center">
                            <MailOutlined style={{ fontSize: 13, color: "rgba(255,255,255,0.9)" }} />
                            <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 12 }}>
                              {userInfo.email}
                            </Text>
                          </Space>
                        )}
                        {userInfo.telephone && (
                          <Space size={6} align="center">
                            <PhoneOutlined style={{ fontSize: 13, color: "rgba(255,255,255,0.9)" }} />
                            <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 12 }}>
                              {userInfo.telephone}
                            </Text>
                          </Space>
                        )}
                      </Space>

                      {/* 注册日期 */}
                      {userInfo.registerDate && (
                        <Text style={{ fontSize: 13, color: "rgba(255,255,255,0.8)", fontWeight: 500 }}>
                          注册于 {userInfo.registerDate}
                        </Text>
                      )}
                    </Space>
                  </Flex>
                </Col>

                {/* 第二列：数据概览区域 - 两行结构 */}
                <Col xs={24} sm={24} md={8} lg={10} xl={12}>
                  <Flex
                    vertical
                    justify="center"
                    style={{ minHeight: "80px", textAlign: "center", padding: "8px 0" }}
                  >
                    {/* 第一行：数据概览标题和图标 */}
                    <Space
                      align="center"
                      style={{
                        justifyContent: "center",
                        marginBottom: 16,
                      }}
                    >
                      <BarChartOutlined
                        style={{
                          fontSize: 16,
                          color: "rgba(255,255,255,0.9)",
                        }}
                      />
                      <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 14, fontWeight: 600 }}>
                        数据概览
                      </Text>
                    </Space>

                    {/* 第二行：指标卡片 */}
                    {statsError ? (
                      <Text style={{ fontSize: 12, color: "rgba(255,255,255,0.8)" }}>
                        数据加载失败
                      </Text>
                    ) : (
                      <Spin spinning={statsLoading}>
                        <Row gutter={[4, 8]} justify="center">
                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{
                                fontSize: 16,
                                fontWeight: 700,
                                color: "white",
                                lineHeight: 1,
                              }}>
                                {personalStats.vehicles}
                              </div>
                              <div style={{
                                fontSize: 11,
                                color: "rgba(255,255,255,0.8)",
                                marginTop: 2,
                              }}>
                                车辆
                              </div>
                            </div>
                          </Col>
                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{
                                fontSize: 16,
                                fontWeight: 700,
                                color: "white",
                                lineHeight: 1,
                              }}>
                                {personalStats.personnel}
                              </div>
                              <div style={{
                                fontSize: 11,
                                color: "rgba(255,255,255,0.8)",
                                marginTop: 2,
                              }}>
                                人员
                              </div>
                            </div>
                          </Col>
                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{
                                fontSize: 16,
                                fontWeight: 700,
                                color: "white",
                                lineHeight: 1,
                              }}>
                                {personalStats.warnings}
                              </div>
                              <div style={{
                                fontSize: 11,
                                color: "rgba(255,255,255,0.8)",
                                marginTop: 2,
                              }}>
                                预警
                              </div>
                            </div>
                          </Col>
                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                            <div style={{ textAlign: 'center' }}>
                              <div style={{
                                fontSize: 16,
                                fontWeight: 700,
                                color: "white",
                                lineHeight: 1,
                              }}>
                                {personalStats.alerts}
                              </div>
                              <div style={{
                                fontSize: 11,
                                color: "rgba(255,255,255,0.8)",
                                marginTop: 2,
                              }}>
                                告警
                              </div>
                            </div>
                          </Col>
                        </Row>
                      </Spin>
                    )}
                  </Flex>
                </Col>

                {/* 第三列：最近活动信息 */}
                <Col xs={24} sm={24} md={8} lg={7} xl={6}>
                  <Flex
                    vertical
                    justify="center"
                    style={{ minHeight: "80px", padding: "8px 0" }}
                  >
                    <Space direction="vertical" size={10}>
                      <Space direction="vertical" size={4}>
                        <Text style={{ fontSize: 12, color: "rgba(255,255,255,0.8)", fontWeight: 500 }}>
                          最后登录时间
                        </Text>
                        <Text style={{ fontSize: 14, color: "white", fontWeight: 600, lineHeight: 1.3 }}>
                          {userInfo.lastLoginTime || "暂无记录"}
                        </Text>
                      </Space>
                      <Space direction="vertical" size={4}>
                        <Text style={{ fontSize: 12, color: "rgba(255,255,255,0.8)", fontWeight: 500 }}>
                          最后登录团队
                        </Text>
                        <Text style={{ fontSize: 14, color: "white", fontWeight: 600, lineHeight: 1.3 }}>
                          {userInfo.lastLoginTeam || "暂无记录"}
                        </Text>
                      </Space>
                    </Space>
                  </Flex>
                </Col>
              </Row>
            </Card>
          </Spin>
        )}
    </>
  );
};

export default UserProfileCard;