import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { message } from 'antd';
import TeamListCard from '../TeamListCard';
import { TeamService } from '@/services/team';
import { AuthService } from '@/services';

// Mock services
jest.mock('@/services/team');
jest.mock('@/services');
jest.mock('@umijs/max', () => ({
  useModel: () => ({
    initialState: {
      currentTeam: { id: 1, name: 'Test Team' },
      fetchTeamInfo: jest.fn(),
      fetchUserInfo: jest.fn(),
    },
    setInitialState: jest.fn(),
  }),
  history: {
    push: jest.fn(),
  },
}));

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

const mockTeamsData = [
  {
    id: 1,
    name: 'Test Team 1',
    memberCount: 5,
    isCreator: true,
    createdAt: '2024-01-01T00:00:00Z',
    stats: {
      vehicles: 10,
      personnel: 5,
      expiring: 2,
      overdue: 1,
    },
  },
  {
    id: 2,
    name: 'Test Team 2',
    memberCount: 3,
    isCreator: false,
    createdAt: '2024-01-02T00:00:00Z',
    stats: {
      vehicles: 5,
      personnel: 3,
      expiring: 0,
      overdue: 0,
    },
  },
];

describe('TeamListCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (TeamService.getUserTeamsWithStats as jest.Mock).mockResolvedValue(mockTeamsData);
  });

  it('should render team list without last access time', async () => {
    render(<TeamListCard />);

    await waitFor(() => {
      expect(screen.getByText('Test Team 1')).toBeInTheDocument();
      expect(screen.getByText('Test Team 2')).toBeInTheDocument();
    });

    // Check that last access time is NOT displayed
    expect(screen.queryByText(/最后登录/)).not.toBeInTheDocument();
    expect(screen.queryByText(/lastAccessTime/)).not.toBeInTheDocument();
  });

  it('should display team basic information correctly', async () => {
    render(<TeamListCard />);

    await waitFor(() => {
      // Check team names
      expect(screen.getByText('Test Team 1')).toBeInTheDocument();
      expect(screen.getByText('Test Team 2')).toBeInTheDocument();

      // Check member counts
      expect(screen.getByText('5 名成员')).toBeInTheDocument();
      expect(screen.getByText('3 名成员')).toBeInTheDocument();

      // Check creation dates
      expect(screen.getByText('创建于 2024/1/1')).toBeInTheDocument();
      expect(screen.getByText('创建于 2024/1/2')).toBeInTheDocument();

      // Check role tags
      expect(screen.getByText('团队管理员')).toBeInTheDocument();
      expect(screen.getByText('团队成员')).toBeInTheDocument();
    });
  });

  it('should display statistics cards correctly', async () => {
    render(<TeamListCard />);

    await waitFor(() => {
      // Check statistics for first team
      expect(screen.getByText('10')).toBeInTheDocument(); // vehicles
      expect(screen.getByText('5')).toBeInTheDocument(); // personnel
      expect(screen.getByText('2')).toBeInTheDocument(); // expiring
      expect(screen.getByText('1')).toBeInTheDocument(); // overdue

      // Check statistics labels
      expect(screen.getAllByText('车辆资源')).toHaveLength(2);
      expect(screen.getAllByText('人员资源')).toHaveLength(2);
      expect(screen.getAllByText('临期事项')).toHaveLength(2);
      expect(screen.getAllByText('逾期事项')).toHaveLength(2);
    });
  });

  it('should handle team switching', async () => {
    (AuthService.selectTeam as jest.Mock).mockResolvedValue({
      teamSelectionSuccess: true,
      team: { id: 2, name: 'Test Team 2' },
    });

    render(<TeamListCard />);

    await waitFor(() => {
      const teamName = screen.getByText('Test Team 2');
      fireEvent.click(teamName);
    });

    await waitFor(() => {
      expect(AuthService.selectTeam).toHaveBeenCalledWith({ teamId: 2 });
    });
  });

  it('should show current team indicator', async () => {
    render(<TeamListCard />);

    await waitFor(() => {
      expect(screen.getByText('当前团队')).toBeInTheDocument();
    });
  });
});
