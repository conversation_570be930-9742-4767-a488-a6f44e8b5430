import React from "react";
import { Card, Col, Row, Spin } from "antd";
import { useModel } from '@umijs/max';

import TodoManagement from './TodoManagement';
import TeamListCard from './TeamListCard';
import UserProfileCard from "./UserProfileCard";
import UserFloatButton from '@/components/FloatButton';

const PersonalCenterPage: React.FC = () => {
  const { initialState, loading } = useModel('@@initialState');

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div style={{
        minHeight: "100vh",
        background: "#f5f8ff",
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
      }}>
        <Spin size="large" />
        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>
      </div>
    );
  }

  // 如果用户未登录，显示错误信息
  if (!initialState?.currentUser) {
    return (
      <div style={{
        minHeight: "100vh",
        background: "#f5f8ff",
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
      }}>
        <div>用户未登录，请先登录</div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: "100vh",
      background: "#f5f8ff",
      padding: "12px 12px 24px 12px" // 移动端减少左右边距
    }}>
      {/* 大的容器区域 */}
      <Card
        style={{
          width: "100%",
          minHeight: "calc(100vh - 48px)",
          borderRadius: "12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)"
        }}
        styles={{
          body: {
            padding: "12px", // 移动端减少内边距
            "@media (min-width: 768px)": {
              padding: "24px"
            }
          }
        }}
      >
        <Row gutter={[16, 16]} style={{ margin: 0 }}>
          {/* 个人信息卡片 - 全宽显示 */}
          <Col xs={24} style={{ marginBottom: 8 }}>
            <UserProfileCard />
          </Col>

          {/* 待办事项 - 响应式布局 */}
          <Col
            xs={24}
            sm={24}
            md={24}
            lg={12}
            xl={12}
            xxl={12}
            style={{ marginBottom: { xs: 8, lg: 0 } }}
          >
            <TodoManagement />
          </Col>

          {/* 团队列表 - 响应式布局 */}
          <Col
            xs={24}
            sm={24}
            md={24}
            lg={12}
            xl={12}
            xxl={12}
          >
            <TeamListCard />
          </Col>
        </Row>
      </Card>

      {/* 添加浮动按钮 */}
      <UserFloatButton />
    </div>
  );
};

export default PersonalCenterPage;