/**
 * 创建团队页面
 */

import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, Space, message } from 'antd';
import { TeamOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { createStyles } from 'antd-style';
import { TeamService, AuthService } from '@/services';
import type { CreateTeamRequest } from '@/types/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundColor: token.colorBgLayout,
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
    },
    formCard: {
      width: '100%',
      maxWidth: 500,
      marginBottom: 24,
    },
    actions: {
      marginTop: 24,
      display: 'flex',
      gap: 16,
      justifyContent: 'center',
    },
    backButton: {
      marginBottom: 24,
    },
  };
});

const CreateTeamPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { styles } = useStyles();
  const { initialState, setInitialState } = useModel('@@initialState');

  const handleSubmit = async (values: CreateTeamRequest) => {
    setLoading(true);
    try {
      const team = await TeamService.createTeam(values);
      message.success('团队创建成功！请在团队列表中选择进入新创建的团队。');

      // 不自动进行团队登录，让用户在团队选择页面手动选择
      // 直接跳转回个人中心整合页面
      history.push('/personal-center');
    } catch (error) {
      console.error('创建团队失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    // 检查是否有 Account Token
    if (AuthService.isLoggedIn()) {
      history.push('/personal-center');
    } else {
      history.push('/user/login');
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={handleBack}
          className={styles.backButton}
        >
          返回
        </Button>

        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            <div>
              <Title level={2}>创建团队</Title>
              <Text type="secondary">创建一个新的团队工作空间</Text>
            </div>
          </Space>
        </div>

        <Card className={styles.formCard}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            autoComplete="off"
          >
            <Form.Item
              label="团队名称"
              name="name"
              rules={[
                { required: true, message: '请输入团队名称！' },
                { max: 100, message: '团队名称长度不能超过100字符！' },
                { min: 2, message: '团队名称至少需要2个字符！' },
              ]}
            >
              <Input
                placeholder="请输入团队名称"
                size="large"
              />
            </Form.Item>

            <Form.Item
              label="团队描述"
              name="description"
              rules={[
                { max: 500, message: '团队描述长度不能超过500字符！' },
              ]}
            >
              <TextArea
                placeholder="请输入团队描述（可选）"
                rows={4}
                showCount
                maxLength={500}
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                block
              >
                创建团队
              </Button>
            </Form.Item>
          </Form>
        </Card>

        <div className={styles.actions}>
          <Space direction="vertical" align="center">
            <Text type="secondary">
              创建团队后，您将成为团队的管理员
            </Text>
            <Text type="secondary">
              可以邀请其他成员加入您的团队
            </Text>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default CreateTeamPage;
