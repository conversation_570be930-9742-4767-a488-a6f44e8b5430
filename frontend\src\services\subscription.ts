/**
 * 订阅管理相关 API 服务
 */

import { apiRequest } from '@/utils/request';
import type {
  SubscriptionPlanResponse,
  CreateSubscriptionRequest,
  SubscriptionResponse,
} from '@/types/api';

/**
 * 订阅服务类
 */
export class SubscriptionService {
  /**
   * 获取所有订阅套餐（公开接口）
   */
  static async getAllPlans(): Promise<SubscriptionPlanResponse[]> {
    const response = await apiRequest.get<SubscriptionPlanResponse[]>('/plans');
    return response.data;
  }

  /**
   * 获取活跃的订阅套餐
   */
  static async getActivePlans(): Promise<SubscriptionPlanResponse[]> {
    const allPlans = await this.getAllPlans();
    return allPlans.filter(plan => plan.isActive);
  }

  /**
   * 根据 ID 获取订阅套餐详情
   */
  static async getPlanById(planId: number): Promise<SubscriptionPlanResponse> {
    const allPlans = await this.getAllPlans();
    const plan = allPlans.find(p => p.id === planId);

    if (!plan) {
      throw new Error('订阅套餐不存在');
    }

    return plan;
  }

  /**
   * 获取当前用户的有效订阅
   */
  static async getCurrentSubscription(): Promise<SubscriptionResponse | null> {
    try {
      const response = await apiRequest.get<SubscriptionResponse>('/subscriptions/current');
      return response.data;
    } catch (error: any) {
      // 如果没有订阅，返回 null
      if (error?.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * 创建订阅
   */
  static async createSubscription(data: CreateSubscriptionRequest): Promise<SubscriptionResponse> {
    const response = await apiRequest.post<SubscriptionResponse>('/subscriptions', data);
    return response.data;
  }

  /**
   * 获取用户订阅列表
   */
  static async getUserSubscriptions(): Promise<SubscriptionResponse[]> {
    const response = await apiRequest.get<SubscriptionResponse[]>('/subscriptions');
    return response.data;
  }



  /**
   * 取消订阅
   */
  static async cancelSubscription(subscriptionId: number): Promise<void> {
    const response = await apiRequest.post<void>(`/subscriptions/${subscriptionId}/cancel`);
    return response.data;
  }

  /**
   * 续费订阅
   */
  static async renewSubscription(
    subscriptionId: number,
    duration: number
  ): Promise<SubscriptionResponse> {
    const response = await apiRequest.post<SubscriptionResponse>(
      `/subscriptions/${subscriptionId}/renew`,
      { duration }
    );
    return response.data;
  }

  /**
   * 升级订阅套餐
   */
  static async upgradeSubscription(
    subscriptionId: number,
    newPlanId: number
  ): Promise<SubscriptionResponse> {
    const response = await apiRequest.post<SubscriptionResponse>(
      `/subscriptions/${subscriptionId}/upgrade`,
      { planId: newPlanId }
    );
    return response.data;
  }

  /**
   * 获取订阅使用统计
   */
  static async getSubscriptionUsage(): Promise<{
    currentUsage: number;
    maxUsage: number;
    usagePercentage: number;
    remainingDays: number;
  }> {
    const currentSubscription = await this.getCurrentSubscription();
    
    if (!currentSubscription) {
      return {
        currentUsage: 0,
        maxUsage: 0,
        usagePercentage: 0,
        remainingDays: 0,
      };
    }

    // 计算剩余天数
    const endDate = new Date(currentSubscription.endDate);
    const now = new Date();
    const remainingDays = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

    // 这里需要后端提供实际的使用量数据
    // 暂时返回模拟数据
    const currentUsage = 0; // 实际使用量
    const maxUsage = currentSubscription.maxSize;
    const usagePercentage = maxUsage > 0 ? (currentUsage / maxUsage) * 100 : 0;

    return {
      currentUsage,
      maxUsage,
      usagePercentage,
      remainingDays,
    };
  }

  /**
   * 获取订阅历史记录
   */
  static async getSubscriptionHistory(): Promise<SubscriptionResponse[]> {
    const subscriptions = await this.getUserSubscriptions();
    
    // 按创建时间倒序排列
    return subscriptions.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  /**
   * 计算套餐价格（考虑折扣等）
   */
  static calculatePlanPrice(plan: SubscriptionPlanResponse, duration: number): {
    originalPrice: number;
    discountedPrice: number;
    discount: number;
    totalPrice: number;
  } {
    const originalPrice = plan.price * duration;
    let discount = 0;

    // 根据订阅时长给予折扣
    if (duration >= 12) {
      discount = 0.2; // 年付8折
    } else if (duration >= 6) {
      discount = 0.1; // 半年付9折
    }

    const discountedPrice = originalPrice * (1 - discount);

    return {
      originalPrice,
      discountedPrice,
      discount: discount * 100,
      totalPrice: discountedPrice,
    };
  }

  /**
   * 比较套餐功能
   */
  static comparePlans(plans: SubscriptionPlanResponse[]): Array<{
    feature: string;
    values: Array<string | number | boolean>;
  }> {
    return [
      {
        feature: '数据存储上限',
        values: plans.map(plan => plan.maxSize),
      },
      {
        feature: '月费价格',
        values: plans.map(plan => `¥${plan.price}`),
      },
      {
        feature: '技术支持',
        values: plans.map(plan => plan.price > 0 ? '7x24小时' : '工作日'),
      },
      {
        feature: '数据备份',
        values: plans.map(plan => plan.price > 0),
      },
      {
        feature: '高级功能',
        values: plans.map(plan => plan.price >= 100),
      },
    ];
  }

  /**
   * 检查订阅状态
   */
  static async checkSubscriptionStatus(): Promise<{
    hasActiveSubscription: boolean;
    isExpiringSoon: boolean;
    daysUntilExpiry: number;
    needsUpgrade: boolean;
  }> {
    const currentSubscription = await this.getCurrentSubscription();
    
    if (!currentSubscription) {
      return {
        hasActiveSubscription: false,
        isExpiringSoon: false,
        daysUntilExpiry: 0,
        needsUpgrade: false,
      };
    }

    const endDate = new Date(currentSubscription.endDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;

    // 检查是否需要升级（基于使用量）
    const usage = await this.getSubscriptionUsage();
    const needsUpgrade = usage.usagePercentage > 80;

    return {
      hasActiveSubscription: true,
      isExpiringSoon,
      daysUntilExpiry,
      needsUpgrade,
    };
  }
}

// 导出默认实例
export default SubscriptionService;
