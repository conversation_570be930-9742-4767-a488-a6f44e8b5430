/**
 * 请求工具类
 * 基于 umi-request 封装，支持双阶段认证
 */

import { extend } from 'umi-request';
import { message } from 'antd';
import type { ApiResponse } from '@/types/api';

// 创建请求实例
const request = extend({
  prefix: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';

  /**
   * 获取当前Token
   */
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * 设置Token
   */
  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * 清除Token
   */
  static clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }

  /**
   * 检查是否有Token
   */
  static hasToken(): boolean {
    return !!this.getToken();
  }




}

/**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */
request.interceptors.request.use((url, options) => {
  const token = TokenManager.getToken();

  if (token) {
    // 添加Authorization头部
    const headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
    };
    return {
      url,
      options: { ...options, headers },
    };
  }

  return { url, options };
});

/**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 */
request.interceptors.response.use(
  async (response) => {
    const data = await response.clone().json();

    // 检查业务状态码
    if (data.code !== 200) {
      // 认证失败的处理
      if (data.code === 401) {
        // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，
        // 可能是Token更新的时序问题，不立即跳转
        const currentPath = window.location.pathname;
        const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');

        // 如果是Dashboard相关页面，延迟处理认证错误
        if (isDashboardRelated) {
          console.warn('Dashboard页面认证失败，可能是Token更新时序问题:', data.message);
          return Promise.reject(new Error(data.message));
        }

        // 其他页面立即处理认证错误
        TokenManager.clearToken();
        message.error('登录已过期，请重新登录');
        // 跳转到登录页，避免重复跳转
        if (window.location.pathname !== '/user/login') {
          window.location.href = '/user/login';
        }
        return Promise.reject(new Error(data.message));
      }

      // 其他业务错误，显示错误消息
      message.error(data.message || '请求失败');
      return Promise.reject(new Error(data.message));
    }

    return response;
  },
  (error) => {
    // 网络错误或其他错误
    if (error.response) {
      const { status } = error.response;
      if (status === 401) {
        TokenManager.clearToken();
        message.error('登录已过期，请重新登录');
        if (window.location.pathname !== '/user/login') {
          window.location.href = '/user/login';
        }
      } else if (status === 403) {
        message.error('没有权限访问该资源');
      } else if (status === 404) {
        message.error('请求的资源不存在');
      } else if (status >= 500) {
        message.error('服务器错误，请稍后重试');
      } else {
        message.error('请求失败');
      }
    } else {
      message.error('网络错误，请检查网络连接');
    }
    
    return Promise.reject(error);
  }
);

// 封装常用的请求方法
export const apiRequest = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.get(url, { params });
  },
  
  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.post(url, { data });
  },
  
  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.put(url, { data });
  },
  
  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.delete(url, { params });
  },
};

// 导出 Token 管理器
export { TokenManager };

// 导出默认请求实例
export default request;
